/**
 * Centralized property mapping system for Ghost ↔ Obsidian synchronization
 *
 * This module defines the canonical mapping between Ghost API properties and
 * Obsidian frontmatter properties, ensuring consistency across all sync operations.
 */

export interface PropertyMapping {
  /** Ghost API property name (lowercase, snake_case) */
  ghostProperty: string;
  /** Obsidian frontmatter property name (Title Case) */
  obsidianProperty: string;
  /** Alternative Obsidian property name (lowercase) for backward compatibility */
  obsidianAlternative?: string;
  /** Whether this property should be included in sync status comparison */
  includeInSync: boolean;
  /** Whether this property should be displayed in the UI */
  displayInUI: boolean;
  /** Custom comparison function for complex properties */
  customComparison?: (ghostValue: any, obsidianValue: any) => 'synced' | 'different' | 'unknown';
  /** Custom value extraction function for Ghost values */
  extractGhostValue?: (ghostPost: any) => any;
  /** Custom value extraction function for Obsidian values */
  extractObsidianValue?: (frontmatter: any) => any;
}

/**
 * Canonical property mappings between Ghost and Obsidian
 * Order matters for display purposes
 */
export const PROPERTY_MAPPINGS: PropertyMapping[] = [
  {
    ghostProperty: 'title',
    obsidianProperty: 'Title',
    obsidianAlternative: 'title',
    includeInSync: true,
    displayInUI: true
  },
  {
    ghostProperty: 'slug',
    obsidianProperty: 'Slug',
    obsidianAlternative: 'slug',
    includeInSync: true,
    displayInUI: true
  },
  {
    ghostProperty: 'status',
    obsidianProperty: 'Status',
    obsidianAlternative: 'status',
    includeInSync: true,
    displayInUI: true
  },
  {
    ghostProperty: 'tags',
    obsidianProperty: 'Tags',
    obsidianAlternative: 'tags',
    includeInSync: true,
    displayInUI: true,
    customComparison: (ghostValue: any[], obsidianValue: any[]) => {
      if (!ghostValue && !obsidianValue) return 'synced';
      if (!ghostValue || !obsidianValue) return 'unknown';

      const ghostTagNames = ghostValue.map(tag => typeof tag === 'string' ? tag : tag.name).sort();
      const obsidianTagNames = Array.isArray(obsidianValue) ? obsidianValue.slice().sort() : [];

      if (ghostTagNames.length !== obsidianTagNames.length) return 'different';

      for (let i = 0; i < ghostTagNames.length; i++) {
        if (ghostTagNames[i] !== obsidianTagNames[i]) return 'different';
      }

      return 'synced';
    },
    extractGhostValue: (ghostPost: any) => ghostPost.tags?.map((tag: any) => tag.name) || [],
    extractObsidianValue: (frontmatter: any) => {
      const tags = frontmatter.Tags || frontmatter.tags;
      return Array.isArray(tags) ? tags : [];
    }
  },
  {
    ghostProperty: 'primary_tag',
    obsidianProperty: 'Primary Tag',
    obsidianAlternative: 'primary_tag',
    includeInSync: true,
    displayInUI: true,
    extractGhostValue: (ghostPost: any) => ghostPost.primary_tag?.name || null,
    extractObsidianValue: (frontmatter: any) => frontmatter['Primary Tag'] || frontmatter.primary_tag || null
  },
  {
    ghostProperty: 'visibility',
    obsidianProperty: 'Visibility',
    obsidianAlternative: 'visibility',
    includeInSync: true,
    displayInUI: true
  },
  {
    ghostProperty: 'featured',
    obsidianProperty: 'Featured',
    obsidianAlternative: 'featured',
    includeInSync: true,
    displayInUI: true,
    customComparison: (ghostValue: any, obsidianValue: any) => {
      // Handle boolean values properly
      const ghostBool = Boolean(ghostValue);
      const obsidianBool = Boolean(obsidianValue);
      return ghostBool === obsidianBool ? 'synced' : 'different';
    }
  },
  {
    ghostProperty: 'feature_image',
    obsidianProperty: 'Featured Image',
    obsidianAlternative: 'feature_image',
    includeInSync: true,
    displayInUI: true,
    extractObsidianValue: (frontmatter: any) => frontmatter['Featured Image'] || frontmatter.feature_image || null
  },
  {
    ghostProperty: 'newsletter',
    obsidianProperty: 'Newsletter',
    obsidianAlternative: 'newsletter',
    includeInSync: true,
    displayInUI: true,
    extractGhostValue: (ghostPost: any) => ghostPost.newsletter?.name || null,
    extractObsidianValue: (frontmatter: any) => frontmatter.Newsletter || frontmatter.newsletter || null
  },
  {
    ghostProperty: 'email_sent',
    obsidianProperty: 'Email Sent',
    obsidianAlternative: 'email_sent',
    includeInSync: true,
    displayInUI: true,
    extractGhostValue: (ghostPost: any) => ghostPost.email ? 'Yes' : 'No',
    extractObsidianValue: (frontmatter: any) => {
      const value = frontmatter['Email Sent'] || frontmatter.email_sent;
      if (value === true || value === 'Yes' || value === 'yes') return 'Yes';
      if (value === false || value === 'No' || value === 'no') return 'No';
      return 'No'; // Default to No
    }
  },
  {
    ghostProperty: 'created_at',
    obsidianProperty: 'Created At',
    obsidianAlternative: 'created_at',
    includeInSync: false, // Don't sync creation dates
    displayInUI: false
  },
  {
    ghostProperty: 'updated_at',
    obsidianProperty: 'Updated At',
    obsidianAlternative: 'updated_at',
    includeInSync: false, // Don't sync update dates
    displayInUI: false
  },
  {
    ghostProperty: 'published_at',
    obsidianProperty: 'Published At',
    obsidianAlternative: 'published_at',
    includeInSync: false, // Don't sync publish dates
    displayInUI: false
  }
];

/**
 * Property mapping utilities
 */
export class PropertyMapper {
  /**
   * Get property mapping by Ghost property name
   */
  static getByGhostProperty(ghostProperty: string): PropertyMapping | undefined {
    return PROPERTY_MAPPINGS.find(mapping => mapping.ghostProperty === ghostProperty);
  }

  /**
   * Get property mapping by Obsidian property name (handles both Title Case and lowercase)
   */
  static getByObsidianProperty(obsidianProperty: string): PropertyMapping | undefined {
    return PROPERTY_MAPPINGS.find(mapping =>
      mapping.obsidianProperty === obsidianProperty ||
      mapping.obsidianAlternative === obsidianProperty
    );
  }

  /**
   * Get all properties that should be included in sync status
   */
  static getSyncProperties(): PropertyMapping[] {
    return PROPERTY_MAPPINGS.filter(mapping => mapping.includeInSync);
  }

  /**
   * Get all properties that should be displayed in UI
   */
  static getDisplayProperties(): PropertyMapping[] {
    return PROPERTY_MAPPINGS.filter(mapping => mapping.displayInUI);
  }

  /**
   * Extract value from Ghost post using property mapping
   */
  static extractGhostValue(ghostPost: any, mapping: PropertyMapping): any {
    if (mapping.extractGhostValue) {
      return mapping.extractGhostValue(ghostPost);
    }
    return ghostPost[mapping.ghostProperty];
  }

  /**
   * Extract value from Obsidian frontmatter using property mapping
   */
  static extractObsidianValue(frontmatter: any, mapping: PropertyMapping): any {
    if (mapping.extractObsidianValue) {
      return mapping.extractObsidianValue(frontmatter);
    }

    // Try Title Case first, then lowercase
    const titleCaseValue = frontmatter[mapping.obsidianProperty];
    if (titleCaseValue !== undefined) {
      return titleCaseValue;
    }

    if (mapping.obsidianAlternative) {
      return frontmatter[mapping.obsidianAlternative];
    }

    return undefined;
  }

  /**
   * Compare values using property mapping
   */
  static compareValues(ghostPost: any, frontmatter: any, mapping: PropertyMapping): 'synced' | 'different' | 'unknown' {
    const ghostValue = this.extractGhostValue(ghostPost, mapping);
    const obsidianValue = this.extractObsidianValue(frontmatter, mapping);

    if (mapping.customComparison) {
      return mapping.customComparison(ghostValue, obsidianValue);
    }

    // Default comparison
    if (ghostValue === undefined && obsidianValue === undefined) return 'synced';
    if (ghostValue === null && obsidianValue === undefined) return 'synced';
    if (ghostValue === undefined && obsidianValue === null) return 'synced';
    if (ghostValue === null && obsidianValue === null) return 'synced';

    // Handle date comparison
    if (typeof ghostValue === 'string' && typeof obsidianValue === 'string') {
      const ghostDate = new Date(ghostValue);
      const obsidianDate = new Date(obsidianValue);
      if (!isNaN(ghostDate.getTime()) && !isNaN(obsidianDate.getTime())) {
        return Math.abs(ghostDate.getTime() - obsidianDate.getTime()) < 1000 ? 'synced' : 'different';
      }
    }

    return ghostValue === obsidianValue ? 'synced' : 'different';
  }

  /**
   * Create a normalized frontmatter object with proper property names
   */
  static normalizeToObsidian(frontmatter: any): any {
    const normalized: any = {};

    // Copy all existing properties first
    Object.assign(normalized, frontmatter);

    // Apply property mappings
    for (const mapping of PROPERTY_MAPPINGS) {
      const value = this.extractObsidianValue(frontmatter, mapping);
      if (value !== undefined) {
        // Set the canonical Title Case property
        normalized[mapping.obsidianProperty] = value;

        // Remove the lowercase alternative if it exists
        if (mapping.obsidianAlternative && mapping.obsidianAlternative !== mapping.obsidianProperty) {
          delete normalized[mapping.obsidianAlternative];
        }
      }
    }

    return normalized;
  }

  /**
   * Create a normalized object with Ghost property names
   */
  static normalizeToGhost(frontmatter: any): any {
    const normalized: any = {};

    // First, copy all properties that don't have mappings
    const mappedObsidianProperties = new Set();
    for (const mapping of PROPERTY_MAPPINGS) {
      mappedObsidianProperties.add(mapping.obsidianProperty);
      if (mapping.obsidianAlternative) {
        mappedObsidianProperties.add(mapping.obsidianAlternative);
      }
    }

    // Copy unmapped properties
    for (const [key, value] of Object.entries(frontmatter)) {
      if (!mappedObsidianProperties.has(key)) {
        normalized[key] = value;
      }
    }

    // Then apply property mappings
    for (const mapping of PROPERTY_MAPPINGS) {
      const value = this.extractObsidianValue(frontmatter, mapping);
      if (value !== undefined) {
        normalized[mapping.ghostProperty] = value;
      }
    }

    return normalized;
  }
}
