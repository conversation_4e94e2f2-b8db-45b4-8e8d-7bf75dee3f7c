import { chromium } from 'playwright';
import type { <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import { expect } from 'vitest';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * E2E Tests for Error Handling
 *
 * These tests verify that the plugin handles various error scenarios gracefully:
 * 1. Invalid Ghost API credentials
 * 2. Network connectivity issues
 * 3. Invalid Ghost URL
 * 4. Missing required fields
 * 5. File system errors
 * 6. Malformed content
 */

/**
 * Wait for async operations to complete
 */
async function waitForAsyncOperation(timeout: number = 1000): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, timeout));
}

/**
 * Helper to create a test file with specific content using Obsidian's vault API
 */
async function createTestFile(page: Page, filePath: string, content: string): Promise<void> {
  await page.evaluate(async ({ path, fileContent }) => {
    try {
      await (window as any).app.vault.create(path, fileContent);
      return true;
    } catch (error) {
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (file) {
        await (window as any).app.vault.modify(file, fileContent);
        return true;
      }
      throw error;
    }
  }, { path: filePath, fileContent: content });
}

/**
 * Helper to open a file in Obsidian
 */
async function openFile(page: Page, filePath: string): Promise<void> {
  await page.evaluate(async ({ path }) => {
    const file = (window as any).app.vault.getAbstractFileByPath(path);
    if (!file) {
      throw new Error(`File not found: ${path}`);
    }
    await (window as any).app.workspace.getLeaf().openFile(file);
  }, { path: filePath });
}

describe("Ghost Sync - Error Handling E2E Tests", () => {
  let browser: Browser;
  let page: Page;
  const articlesDir = path.join(__dirname, '../../tests/vault/Test/articles');

  beforeAll(async () => {
    // Connect to existing Obsidian instance via CDP
    browser = await chromium.connectOverCDP('http://127.0.0.1:9222');

    // Get the first page (Obsidian window)
    const contexts = browser.contexts();
    const context = contexts[0];
    const pages = context.pages();
    page = pages[0];

    console.log("Connected to Obsidian via Playwright");

    // Enable the ghost-sync plugin
    await page.evaluate(() => {
      (window as any).app.plugins.setEnable(true);
      (window as any).app.plugins.enablePlugin('ghost-sync');
    });

    await waitForAsyncOperation(500);
  });

  afterAll(async () => {
    if (browser) {
      await browser.close();
    }
  });

  beforeEach(async () => {
    // Clear any existing test files
    if (fs.existsSync(articlesDir)) {
      const files = fs.readdirSync(articlesDir);
      for (const file of files) {
        if (file.includes('error-test') && file.endsWith('.md')) {
          fs.unlinkSync(path.join(articlesDir, file));
        }
      }
    }
    await waitForAsyncOperation(200);
  });

  test("should handle missing API key gracefully", async () => {
    // Temporarily clear the API key
    const originalSettings = await page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const original = { ...plugin.settings };
      
      // Clear the API key
      plugin.settings.ghostAdminApiKey = '';
      
      return original;
    });

    // Try to sync a post without API key
    const testSlug = "error-test-no-api-key";
    const relativeFilePath = `articles/${testSlug}.md`;

    const content = `---
Title: "Error Test No API Key"
Slug: "${testSlug}"
Status: "draft"
---

# Error Test

This should fail due to missing API key.`;

    await createTestFile(page, relativeFilePath, content);
    await openFile(page, relativeFilePath);
    await waitForAsyncOperation(500);

    // Try to sync
    await page.evaluate(() => {
      (window as any).app.commands.executeCommandById('ghost-sync:sync-current-to-ghost');
    });

    await waitForAsyncOperation(2000);

    // Check for error notice
    const errorResult = await page.evaluate(() => {
      const notices = document.querySelectorAll('.notice');
      const noticeTexts = Array.from(notices).map(n => n.textContent?.toLowerCase() || '');
      
      const hasApiKeyError = noticeTexts.some(text => 
        text.includes('api key') || 
        text.includes('not configured') ||
        text.includes('settings')
      );

      return {
        hasApiKeyError,
        allNotices: noticeTexts
      };
    });

    expect(errorResult.hasApiKeyError).toBe(true);

    console.log(`✅ Properly handled missing API key`);
    console.log(`Error notices: ${errorResult.allNotices.join(', ')}`);

    // Restore original settings
    await page.evaluate(({ settings }) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      plugin.settings = settings;
    }, { settings: originalSettings });
  });

  test("should handle invalid Ghost URL gracefully", async () => {
    // Temporarily set invalid URL
    const originalSettings = await page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const original = { ...plugin.settings };
      
      // Set invalid URL
      plugin.settings.ghostUrl = 'invalid-url-format';
      
      return original;
    });

    // Try to sync a post with invalid URL
    const testSlug = "error-test-invalid-url";
    const relativeFilePath = `articles/${testSlug}.md`;

    const content = `---
Title: "Error Test Invalid URL"
Slug: "${testSlug}"
Status: "draft"
---

# Error Test

This should fail due to invalid URL.`;

    await createTestFile(page, relativeFilePath, content);
    await openFile(page, relativeFilePath);
    await waitForAsyncOperation(500);

    // Try to sync
    await page.evaluate(() => {
      (window as any).app.commands.executeCommandById('ghost-sync:sync-current-to-ghost');
    });

    await waitForAsyncOperation(2000);

    // Check for error notice
    const errorResult = await page.evaluate(() => {
      const notices = document.querySelectorAll('.notice');
      const noticeTexts = Array.from(notices).map(n => n.textContent?.toLowerCase() || '');
      
      const hasUrlError = noticeTexts.some(text => 
        text.includes('url') || 
        text.includes('invalid') ||
        text.includes('error') ||
        text.includes('failed')
      );

      return {
        hasUrlError,
        allNotices: noticeTexts
      };
    });

    expect(errorResult.hasUrlError).toBe(true);

    console.log(`✅ Properly handled invalid Ghost URL`);
    console.log(`Error notices: ${errorResult.allNotices.join(', ')}`);

    // Restore original settings
    await page.evaluate(({ settings }) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      plugin.settings = settings;
    }, { settings: originalSettings });
  });

  test("should handle malformed frontmatter gracefully", async () => {
    const testSlug = "error-test-malformed-frontmatter";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create file with malformed frontmatter
    const content = `---
Title: "Error Test Malformed
Slug: ${testSlug}
Status: draft
Invalid: yaml: content: here
---

# Error Test

This has malformed frontmatter.`;

    await createTestFile(page, relativeFilePath, content);
    await openFile(page, relativeFilePath);
    await waitForAsyncOperation(500);

    // Try to sync
    const syncResult = await page.evaluate(() => {
      try {
        (window as any).app.commands.executeCommandById('ghost-sync:sync-current-to-ghost');
        return { executed: true, error: null };
      } catch (error) {
        return { executed: false, error: error.message };
      }
    });

    await waitForAsyncOperation(2000);

    // Check for error handling
    const errorResult = await page.evaluate(() => {
      const notices = document.querySelectorAll('.notice');
      const noticeTexts = Array.from(notices).map(n => n.textContent || '');
      
      return {
        hasNotices: notices.length > 0,
        noticeTexts: noticeTexts
      };
    });

    // Either the command should execute (and possibly show error) or fail gracefully
    expect(syncResult.executed || errorResult.hasNotices).toBe(true);

    console.log(`✅ Handled malformed frontmatter gracefully`);
    console.log(`Command executed: ${syncResult.executed}`);
    console.log(`Notices: ${errorResult.noticeTexts.join(', ')}`);
  });

  test("should handle file without frontmatter gracefully", async () => {
    const relativeFilePath = `articles/error-test-no-frontmatter.md`;

    // Create file without frontmatter
    const content = `# Error Test No Frontmatter

This file has no frontmatter at all.

It should be handled gracefully.`;

    await createTestFile(page, relativeFilePath, content);
    await openFile(page, relativeFilePath);
    await waitForAsyncOperation(500);

    // Try to sync
    const syncResult = await page.evaluate(() => {
      try {
        (window as any).app.commands.executeCommandById('ghost-sync:sync-current-to-ghost');
        return { executed: true, error: null };
      } catch (error) {
        return { executed: false, error: error.message };
      }
    });

    await waitForAsyncOperation(2000);

    // Check for appropriate error handling
    const errorResult = await page.evaluate(() => {
      const notices = document.querySelectorAll('.notice');
      const noticeTexts = Array.from(notices).map(n => n.textContent?.toLowerCase() || '');
      
      const hasSlugError = noticeTexts.some(text => 
        text.includes('slug') || 
        text.includes('frontmatter') ||
        text.includes('title')
      );

      return {
        hasSlugError,
        allNotices: noticeTexts
      };
    });

    expect(syncResult.executed).toBe(true);
    expect(errorResult.hasSlugError).toBe(true);

    console.log(`✅ Handled file without frontmatter gracefully`);
    console.log(`Error notices: ${errorResult.allNotices.join(', ')}`);
  });

  test("should handle network errors gracefully", async () => {
    // Temporarily set unreachable URL
    const originalSettings = await page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const original = { ...plugin.settings };
      
      // Set unreachable URL
      plugin.settings.ghostUrl = 'https://unreachable-ghost-site-12345.com';
      
      return original;
    });

    const testSlug = "error-test-network-error";
    const relativeFilePath = `articles/${testSlug}.md`;

    const content = `---
Title: "Error Test Network Error"
Slug: "${testSlug}"
Status: "draft"
---

# Error Test

This should fail due to network error.`;

    await createTestFile(page, relativeFilePath, content);
    await openFile(page, relativeFilePath);
    await waitForAsyncOperation(500);

    // Try to sync
    await page.evaluate(() => {
      (window as any).app.commands.executeCommandById('ghost-sync:sync-current-to-ghost');
    });

    await waitForAsyncOperation(5000); // Longer timeout for network error

    // Check for network error notice
    const errorResult = await page.evaluate(() => {
      const notices = document.querySelectorAll('.notice');
      const noticeTexts = Array.from(notices).map(n => n.textContent?.toLowerCase() || '');
      
      const hasNetworkError = noticeTexts.some(text => 
        text.includes('network') || 
        text.includes('connection') ||
        text.includes('failed') ||
        text.includes('error') ||
        text.includes('unreachable')
      );

      return {
        hasNetworkError,
        allNotices: noticeTexts
      };
    });

    expect(errorResult.hasNetworkError).toBe(true);

    console.log(`✅ Properly handled network error`);
    console.log(`Error notices: ${errorResult.allNotices.join(', ')}`);

    // Restore original settings
    await page.evaluate(({ settings }) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      plugin.settings = settings;
    }, { settings: originalSettings });
  });

  test("should handle command execution on non-markdown files", async () => {
    // Try to execute sync command when a non-markdown file is active
    const nonMarkdownResult = await page.evaluate(() => {
      // Try to get a non-markdown file or create a scenario where no file is active
      const activeFile = (window as any).app.workspace.getActiveFile();
      
      try {
        (window as any).app.commands.executeCommandById('ghost-sync:sync-current-to-ghost');
        return { executed: true, activeFile: activeFile?.name || 'none' };
      } catch (error) {
        return { executed: false, error: error.message, activeFile: activeFile?.name || 'none' };
      }
    });

    await waitForAsyncOperation(1000);

    // Check for appropriate handling
    const noticeResult = await page.evaluate(() => {
      const notices = document.querySelectorAll('.notice');
      const noticeTexts = Array.from(notices).map(n => n.textContent || '');
      
      return {
        hasNotices: notices.length > 0,
        noticeTexts: noticeTexts
      };
    });

    // Command should either execute gracefully or show appropriate notice
    expect(nonMarkdownResult.executed || noticeResult.hasNotices).toBe(true);

    console.log(`✅ Handled non-markdown file scenario gracefully`);
    console.log(`Active file: ${nonMarkdownResult.activeFile}`);
    console.log(`Executed: ${nonMarkdownResult.executed}`);
    console.log(`Notices: ${noticeResult.noticeTexts.join(', ')}`);
  });
});
