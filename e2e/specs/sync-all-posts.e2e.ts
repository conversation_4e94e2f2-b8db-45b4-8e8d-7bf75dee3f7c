import { chromium } from 'playwright';
import type { <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import { expect } from 'vitest';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';
import { setupGhostAPIMocking, shouldRecordGhostAPI } from '../fixtures/ghost-api-helper';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * E2E Tests for Sync All Posts from Ghost
 *
 * These tests verify the bulk sync functionality:
 * 1. Sync all posts from Ghost via command palette
 * 2. Sync all posts via direct command execution
 * 3. Handle large numbers of posts
 * 4. Verify progress feedback during bulk sync
 * 5. Handle errors during bulk sync
 */

/**
 * Wait for async operations to complete
 */
async function waitForAsyncOperation(timeout: number = 1000): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, timeout));
}

/**
 * Wait for files to be created in a directory with smart polling
 */
async function waitForFileCreation(
  directory: string,
  expectedMinCount: number = 1,
  timeout: number = 10000
): Promise<string[]> {
  const startTime = Date.now();
  let lastFileCount = 0;

  while (Date.now() - startTime < timeout) {
    if (fs.existsSync(directory)) {
      const files = fs.readdirSync(directory).filter(file => file.endsWith('.md'));

      if (files.length !== lastFileCount) {
        console.log(`📁 Found ${files.length} files (expecting at least ${expectedMinCount})`);
        lastFileCount = files.length;
      }

      if (files.length >= expectedMinCount) {
        return files;
      }
    }

    await new Promise(resolve => setTimeout(resolve, 100));
  }

  const actualFiles = fs.existsSync(directory)
    ? fs.readdirSync(directory).filter(file => file.endsWith('.md'))
    : [];

  console.log(`Timeout reached. Found ${actualFiles.length} files: [${actualFiles.join(', ')}]`);
  return actualFiles;
}

describe("Ghost Sync - Sync All Posts E2E Tests", () => {
  let browser: Browser;
  let page: Page;
  const articlesDir = path.join(__dirname, '../../tests/vault/Test/articles');

  beforeAll(async () => {
    // Connect to existing Obsidian instance via CDP
    browser = await chromium.connectOverCDP('http://127.0.0.1:9222');

    // Get the first page (Obsidian window)
    const contexts = browser.contexts();
    const context = contexts[0];
    const pages = context.pages();
    page = pages[0];

    console.log("Connected to Obsidian via Playwright");

    // Set up Ghost API mocking for sync all posts scenarios
    await setupGhostAPIMocking(page, context, {
      ghostUrl: 'https://solnic.ghost.io',
      scenario: 'sync-all-posts',
      record: shouldRecordGhostAPI()
    });

    // Enable the ghost-sync plugin
    await page.evaluate(() => {
      (window as any).app.plugins.setEnable(true);
      (window as any).app.plugins.enablePlugin('ghost-sync');
    });

    await waitForAsyncOperation(500);
  });

  afterAll(async () => {
    if (browser) {
      await browser.close();
    }
  });

  beforeEach(async () => {
    // Clear any existing test files
    if (fs.existsSync(articlesDir)) {
      const files = fs.readdirSync(articlesDir);
      for (const file of files) {
        if (file.endsWith('.md')) {
          fs.unlinkSync(path.join(articlesDir, file));
        }
      }
    }
    await waitForAsyncOperation(200);
  });

  test("should sync all posts from Ghost via command palette", async () => {
    // Open command palette
    await page.keyboard.down('Meta'); // Cmd on Mac
    await page.keyboard.press('KeyP');
    await page.keyboard.up('Meta');

    // Type the sync all command
    await page.keyboard.type('Sync all posts from Ghost to local');
    await page.keyboard.press('Enter');

    console.log("Executed sync all posts command via command palette");

    // Wait for sync operation to complete (longer timeout for bulk operation)
    await waitForAsyncOperation(5000);

    // Check for completion notices
    const syncResult = await page.evaluate(() => {
      const notices = document.querySelectorAll('.notice');
      const noticeTexts = Array.from(notices).map(n => n.textContent || '');
      
      const hasCompletionNotice = noticeTexts.some(text => 
        text.toLowerCase().includes('sync') && 
        (text.toLowerCase().includes('complete') || 
         text.toLowerCase().includes('finished') ||
         text.toLowerCase().includes('done'))
      );

      const hasErrorNotice = noticeTexts.some(text => 
        text.toLowerCase().includes('error') || 
        text.toLowerCase().includes('failed')
      );

      return {
        hasCompletionNotice,
        hasErrorNotice,
        allNotices: noticeTexts,
        noticeCount: notices.length
      };
    });

    // Either completion notice or some files should be created
    const files = await waitForFileCreation(articlesDir, 0, 3000);
    
    expect(syncResult.hasCompletionNotice || files.length > 0).toBe(true);

    console.log(`✅ Sync all posts executed via command palette`);
    console.log(`Files created: ${files.length}`);
    console.log(`Notices: ${syncResult.allNotices.join(', ')}`);
  });

  test("should sync all posts via direct command execution", async () => {
    // Execute the sync all command directly
    const executionResult = await page.evaluate(() => {
      try {
        (window as any).app.commands.executeCommandById('ghost-sync:sync-all-from-ghost');
        return { success: true, error: null };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    expect(executionResult.success).toBe(true);

    console.log("Executed sync all posts command directly");

    // Wait for sync operation to complete
    await waitForAsyncOperation(5000);

    // Check for results
    const syncResult = await page.evaluate(() => {
      const notices = document.querySelectorAll('.notice');
      const noticeTexts = Array.from(notices).map(n => n.textContent || '');
      
      return {
        hasNotices: notices.length > 0,
        noticeTexts: noticeTexts
      };
    });

    // Check for created files
    const files = await waitForFileCreation(articlesDir, 0, 3000);

    // Either notices should appear or files should be created
    expect(syncResult.hasNotices || files.length > 0).toBe(true);

    console.log(`✅ Sync all posts executed directly`);
    console.log(`Files created: ${files.length}`);
    console.log(`Notices: ${syncResult.noticeTexts.join(', ')}`);
  });

  test("should verify sync all command is registered", async () => {
    // Verify the command exists and is properly registered
    const commandCheck = await page.evaluate(() => {
      const commands = (window as any).app.commands.commands;
      const syncAllCommand = commands['ghost-sync:sync-all-from-ghost'];
      
      return {
        commandExists: !!syncAllCommand,
        commandName: syncAllCommand?.name,
        commandId: syncAllCommand?.id
      };
    });

    expect(commandCheck.commandExists).toBe(true);
    expect(commandCheck.commandId).toBe('ghost-sync:sync-all-from-ghost');
    expect(commandCheck.commandName).toBe('Sync all posts from Ghost to local');

    console.log(`✅ Sync all posts command is properly registered`);
    console.log(`Command: ${commandCheck.commandName} (${commandCheck.commandId})`);
  });

  test("should handle sync all operation with progress feedback", async () => {
    // Execute sync all and monitor for progress feedback
    await page.evaluate(() => {
      (window as any).app.commands.executeCommandById('ghost-sync:sync-all-from-ghost');
    });

    console.log("Started sync all operation, monitoring progress...");

    // Monitor notices over time to catch progress updates
    const progressMonitoring = [];
    const monitoringDuration = 6000; // 6 seconds
    const checkInterval = 500; // Check every 500ms

    for (let i = 0; i < monitoringDuration / checkInterval; i++) {
      await waitForAsyncOperation(checkInterval);
      
      const currentNotices = await page.evaluate(() => {
        const notices = document.querySelectorAll('.notice');
        return Array.from(notices).map(n => n.textContent || '');
      });

      if (currentNotices.length > 0) {
        progressMonitoring.push({
          time: i * checkInterval,
          notices: [...currentNotices]
        });
      }
    }

    // Check final state
    const finalFiles = await waitForFileCreation(articlesDir, 0, 1000);

    console.log(`✅ Monitored sync all operation`);
    console.log(`Progress updates captured: ${progressMonitoring.length}`);
    console.log(`Final files created: ${finalFiles.length}`);

    if (progressMonitoring.length > 0) {
      console.log(`Sample progress notices:`, progressMonitoring[0].notices);
    }

    // Either progress was shown or files were created
    expect(progressMonitoring.length > 0 || finalFiles.length > 0).toBe(true);
  });

  test("should handle empty Ghost site gracefully", async () => {
    // This test verifies behavior when Ghost has no posts
    await page.evaluate(() => {
      (window as any).app.commands.executeCommandById('ghost-sync:sync-all-from-ghost');
    });

    console.log("Testing sync all with potentially empty Ghost site");

    await waitForAsyncOperation(3000);

    // Check for appropriate messaging
    const emptyResult = await page.evaluate(() => {
      const notices = document.querySelectorAll('.notice');
      const noticeTexts = Array.from(notices).map(n => n.textContent?.toLowerCase() || '');
      
      const hasEmptyNotice = noticeTexts.some(text => 
        text.includes('no posts') || 
        text.includes('empty') ||
        text.includes('0 posts') ||
        text.includes('complete')
      );

      return {
        hasEmptyNotice,
        allNotices: noticeTexts
      };
    });

    const files = fs.existsSync(articlesDir) 
      ? fs.readdirSync(articlesDir).filter(file => file.endsWith('.md'))
      : [];

    // Either appropriate notice should appear or operation should complete silently
    expect(emptyResult.hasEmptyNotice || files.length === 0).toBe(true);

    console.log(`✅ Handled empty Ghost site appropriately`);
    console.log(`Notices: ${emptyResult.allNotices.join(', ')}`);
    console.log(`Files created: ${files.length}`);
  });

  test("should execute sync all without throwing errors", async () => {
    // Test that the command executes without JavaScript errors
    const errorsBefore = await page.evaluate(() => {
      return (window as any).errorCount || 0;
    });

    const executionResult = await page.evaluate(() => {
      try {
        (window as any).app.commands.executeCommandById('ghost-sync:sync-all-from-ghost');
        return { success: true, error: null };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    expect(executionResult.success).toBe(true);

    await waitForAsyncOperation(3000);

    const errorsAfter = await page.evaluate(() => {
      return (window as any).errorCount || 0;
    });

    // No new JavaScript errors should have occurred
    expect(errorsAfter).toBe(errorsBefore);

    console.log(`✅ Sync all command executed without JavaScript errors`);
  });
});
