import { chromium } from 'playwright';
import type { <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import { expect } from 'vitest';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';
import { setupGhostAPIMocking, shouldRecordGhostAPI } from '../fixtures/ghost-api-helper';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * E2E Tests for Sync Current Post to Ghost
 *
 * These tests verify the main sync functionality that users would use most:
 * 1. Sync current post to <PERSON> via command palette
 * 2. Sync current post to <PERSON> via ribbon icon
 * 3. Sync current post to <PERSON> via direct command
 * 4. Handle posts without slugs
 * 5. Handle new posts vs existing posts
 * 6. Verify sync metadata is updated after successful sync
 */

/**
 * Wait for async operations to complete
 */
async function waitForAsyncOperation(timeout: number = 1000): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, timeout));
}

/**
 * Helper to create a test file with specific content using Obsidian's vault API
 */
async function createTestFile(page: Page, filePath: string, content: string): Promise<void> {
  await page.evaluate(async ({ path, fileContent }) => {
    try {
      // Create the file using Obsidian's vault API
      await (window as any).app.vault.create(path, fileContent);
      return true;
    } catch (error) {
      // If file already exists, modify it instead
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (file) {
        await (window as any).app.vault.modify(file, fileContent);
        return true;
      }
      throw error;
    }
  }, { path: filePath, fileContent: content });
}

/**
 * Helper to open a file in Obsidian
 */
async function openFile(page: Page, filePath: string): Promise<void> {
  await page.evaluate(async ({ path }) => {
    const file = (window as any).app.vault.getAbstractFileByPath(path);
    if (!file) {
      throw new Error(`File not found: ${path}`);
    }
    await (window as any).app.workspace.getLeaf().openFile(file);
  }, { path: filePath });
}

describe("Ghost Sync - Sync Current Post E2E Tests", () => {
  let browser: Browser;
  let page: Page;
  const articlesDir = path.join(__dirname, '../../tests/vault/Test/articles');

  beforeAll(async () => {
    // Connect to existing Obsidian instance via CDP
    browser = await chromium.connectOverCDP('http://127.0.0.1:9222');

    // Get the first page (Obsidian window)
    const contexts = browser.contexts();
    const context = contexts[0];
    const pages = context.pages();
    page = pages[0];

    console.log("Connected to Obsidian via Playwright");

    // Set up Ghost API mocking for sync current post scenarios
    await setupGhostAPIMocking(page, context, {
      ghostUrl: 'https://solnic.ghost.io',
      scenario: 'sync-current-post',
      record: shouldRecordGhostAPI()
    });

    // Enable the ghost-sync plugin
    await page.evaluate(() => {
      (window as any).app.plugins.setEnable(true);
      (window as any).app.plugins.enablePlugin('ghost-sync');
    });

    await waitForAsyncOperation(500);
  });

  afterAll(async () => {
    if (browser) {
      await browser.close();
    }
  });

  beforeEach(async () => {
    // Clear any existing test files
    if (fs.existsSync(articlesDir)) {
      const files = fs.readdirSync(articlesDir);
      for (const file of files) {
        if (file.includes('sync-current-test') && file.endsWith('.md')) {
          fs.unlinkSync(path.join(articlesDir, file));
        }
      }
    }
    await waitForAsyncOperation(200);
  });

  test("should sync current post to Ghost via command palette", async () => {
    const testTitle = "Sync Current Test Post";
    const testSlug = "sync-current-test-post";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create a test post
    const content = `---
Title: "${testTitle}"
Slug: "${testSlug}"
Status: "draft"
Featured Image: null
Newsletter: null
---

# ${testTitle}

This is a test post for syncing current post to Ghost via command palette.

## Test Content

Some content to verify the sync works correctly.`;

    await createTestFile(page, relativeFilePath, content);
    await openFile(page, relativeFilePath);
    await waitForAsyncOperation(500);

    // Open command palette and sync current post
    await page.keyboard.down('Meta'); // Cmd on Mac
    await page.keyboard.press('KeyP');
    await page.keyboard.up('Meta');

    // Type the sync command
    await page.keyboard.type('Sync current post to Ghost');
    await page.keyboard.press('Enter');

    console.log("Executed sync current post command via command palette");

    // Wait for sync operation to complete
    await waitForAsyncOperation(2000);

    // Verify sync was successful by checking for success notice or sync metadata
    const syncResult = await page.evaluate(async ({ path }) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      
      if (!plugin || !file) {
        throw new Error('Plugin or file not found');
      }

      // Check if sync metadata was updated (indicates successful sync)
      const syncedAt = plugin.syncMetadata.getSyncedAt(file);
      
      return {
        hasSyncedAt: !!syncedAt,
        syncedAt: syncedAt,
        fileExists: true
      };
    }, { path: relativeFilePath });

    expect(syncResult.fileExists).toBe(true);
    expect(syncResult.hasSyncedAt).toBe(true);

    console.log(`✅ Successfully synced current post via command palette`);
    console.log(`Synced at: ${syncResult.syncedAt}`);
  });

  test("should sync current post to Ghost via ribbon icon", async () => {
    const testTitle = "Ribbon Sync Test Post";
    const testSlug = "ribbon-sync-test-post";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create a test post
    const content = `---
Title: "${testTitle}"
Slug: "${testSlug}"
Status: "draft"
---

# ${testTitle}

This is a test post for syncing via ribbon icon.`;

    await createTestFile(page, relativeFilePath, content);
    await openFile(page, relativeFilePath);
    await waitForAsyncOperation(500);

    // Click the sync ribbon icon
    const ribbonIcon = page.locator('.side-dock-ribbon .clickable-icon[aria-label*="Sync current post"]');
    await ribbonIcon.click();

    console.log("Clicked sync ribbon icon");

    // Wait for sync operation to complete
    await waitForAsyncOperation(2000);

    // Verify sync was successful
    const syncResult = await page.evaluate(async ({ path }) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      
      if (!plugin || !file) {
        throw new Error('Plugin or file not found');
      }

      const syncedAt = plugin.syncMetadata.getSyncedAt(file);
      
      return {
        hasSyncedAt: !!syncedAt,
        syncedAt: syncedAt
      };
    }, { path: relativeFilePath });

    expect(syncResult.hasSyncedAt).toBe(true);

    console.log(`✅ Successfully synced current post via ribbon icon`);
  });

  test("should handle post without slug gracefully", async () => {
    const testTitle = "Post Without Slug";
    const relativeFilePath = `articles/post-without-slug.md`;

    // Create a test post without a slug
    const content = `---
Title: "${testTitle}"
Status: "draft"
---

# ${testTitle}

This post has no slug and should show an error.`;

    await createTestFile(page, relativeFilePath, content);
    await openFile(page, relativeFilePath);
    await waitForAsyncOperation(500);

    // Try to sync via direct command execution
    const syncResult = await page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      if (!plugin) {
        throw new Error('Plugin not found');
      }

      // Execute sync and capture any errors
      try {
        plugin.syncCurrentPostToGhost();
        return { success: true, error: null };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // Wait for any notices to appear
    await waitForAsyncOperation(1000);

    // Check for error notice in the UI
    const hasErrorNotice = await page.evaluate(() => {
      const notices = document.querySelectorAll('.notice');
      return Array.from(notices).some(notice => 
        notice.textContent?.includes('slug') || 
        notice.textContent?.includes('Slug')
      );
    });

    // Either the sync should fail gracefully or show an error notice
    expect(hasErrorNotice || !syncResult.success).toBe(true);

    console.log(`✅ Properly handled post without slug`);
  });

  test("should sync current post via direct command execution", async () => {
    const testTitle = "Direct Command Sync Test";
    const testSlug = "direct-command-sync-test";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create a test post
    const content = `---
Title: "${testTitle}"
Slug: "${testSlug}"
Status: "draft"
---

# ${testTitle}

This is a test post for direct command execution.`;

    await createTestFile(page, relativeFilePath, content);
    await openFile(page, relativeFilePath);
    await waitForAsyncOperation(500);

    // Execute the sync command directly
    await page.evaluate(() => {
      (window as any).app.commands.executeCommandById('ghost-sync:sync-current-to-ghost');
    });

    console.log("Executed sync command directly");

    // Wait for sync operation to complete
    await waitForAsyncOperation(2000);

    // Verify sync was successful
    const syncResult = await page.evaluate(async ({ path }) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      
      if (!plugin || !file) {
        throw new Error('Plugin or file not found');
      }

      const syncedAt = plugin.syncMetadata.getSyncedAt(file);
      
      return {
        hasSyncedAt: !!syncedAt,
        syncedAt: syncedAt
      };
    }, { path: relativeFilePath });

    expect(syncResult.hasSyncedAt).toBe(true);

    console.log(`✅ Successfully synced current post via direct command`);
  });
});
