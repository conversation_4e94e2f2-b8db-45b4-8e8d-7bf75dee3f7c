import { chromium } from 'playwright';
import type { <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import { expect } from 'vitest';
import * as path from 'path';
import { fileURLToPath } from 'url';
import { setupGhostAPIMocking, shouldRecordGhostAPI } from '../fixtures/ghost-api-helper';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Example E2E Test demonstrating Ghost API mocking
 *
 * This test shows how to:
 * 1. Set up Ghost API mocking with HAR recording/replaying
 * 2. Test Ghost API interactions without making real API calls
 * 3. Record new interactions when needed
 *
 * To record new interactions:
 *   GHOST_API_RECORD=true npm run test:e2e -- ghost-api-mocking-example.e2e.ts
 *
 * To run with mocked responses:
 *   npm run test:e2e -- ghost-api-mocking-example.e2e.ts
 */

describe("Ghost API Mocking Example", () => {
  let browser: Browser;
  let page: Page;

  beforeAll(async () => {
    // Connect to existing Obsidian instance via CDP
    browser = await chromium.connectOverCDP('http://127.0.0.1:9222');

    // Get the first page (Obsidian window)
    const contexts = browser.contexts();
    const context = contexts[0];
    const pages = context.pages();
    page = pages[0];

    console.log("Connected to Obsidian via Playwright");

    // Set up Ghost API mocking
    // This will either record new interactions or replay existing ones
    await setupGhostAPIMocking(page, context, {
      ghostUrl: 'https://solnic.ghost.io', // Replace with your Ghost URL
      scenario: 'api-mocking-example',
      record: shouldRecordGhostAPI()
    });

    console.log(`Ghost API mocking setup complete. Recording: ${shouldRecordGhostAPI()}`);

    // Enable the ghost-sync plugin
    await page.evaluate(() => {
      (window as any).app.plugins.setEnable(true);
      (window as any).app.plugins.enablePlugin('ghost-sync');
    });

    // Wait for plugin to be ready
    await new Promise(resolve => setTimeout(resolve, 1000));
  });

  afterAll(async () => {
    if (browser) {
      await browser.close();
    }
  });

  test("should mock Ghost API calls when browsing posts", async () => {
    console.log("Testing Ghost API mocking with browse posts command");

    // Execute the browse posts command
    await page.evaluate(() => {
      console.log('Executing command: ghost-sync:browse-posts');
      (window as any).app.commands.executeCommandById('ghost-sync:browse-posts');
    });

    // Wait for the command to execute and API calls to be made/mocked
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check that the command executed without errors
    // In a real test, you would check for specific UI elements or behaviors
    const notices = await page.evaluate(() => {
      // Check if any error notices were shown
      const noticeElements = document.querySelectorAll('.notice');
      return Array.from(noticeElements).map(el => el.textContent);
    });

    console.log("Notices shown:", notices);

    // Verify no error notices were shown
    const errorNotices = notices.filter(notice =>
      notice?.toLowerCase().includes('error') ||
      notice?.toLowerCase().includes('failed')
    );

    expect(errorNotices.length).toBe(0);
  });

  test("should handle Ghost API calls when syncing posts", async () => {
    console.log("Testing Ghost API mocking with sync all posts command");

    // Execute the sync all posts command
    await page.evaluate(() => {
      console.log('Executing command: ghost-sync:sync-all-from-ghost');
      (window as any).app.commands.executeCommandById('ghost-sync:sync-all-from-ghost');
    });

    // Wait for the command to execute and API calls to be made/mocked
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Check that the command executed without errors
    const notices = await page.evaluate(() => {
      const noticeElements = document.querySelectorAll('.notice');
      return Array.from(noticeElements).map(el => el.textContent);
    });

    console.log("Sync notices shown:", notices);

    // Verify the sync command ran (should show some kind of completion notice)
    const hasCompletionNotice = notices.some(notice =>
      notice?.toLowerCase().includes('sync') ||
      notice?.toLowerCase().includes('complete') ||
      notice?.toLowerCase().includes('posts')
    );

    expect(hasCompletionNotice).toBe(true);
  });

  test("should demonstrate network interception is working", async () => {
    console.log("Verifying that network requests are being intercepted");

    // Monitor network requests to verify mocking is working
    const networkRequests: string[] = [];
    const allRequests: string[] = [];

    page.on('request', request => {
      const url = request.url();
      allRequests.push(url);

      if (url.includes('ghost') && url.includes('api')) {
        networkRequests.push(url);
        console.log(`Intercepted Ghost API request: ${url}`);
      }
    });

    // Check if plugin has Ghost settings configured
    const hasSettings = await page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      if (!plugin) return false;

      console.log('Plugin settings:', plugin.settings);
      return !!(plugin.settings?.ghostUrl && plugin.settings?.ghostAdminApiKey);
    });

    console.log(`Plugin has Ghost settings configured: ${hasSettings}`);

    if (hasSettings) {
      // Execute a command that makes Ghost API calls
      await page.evaluate(() => {
        (window as any).app.commands.executeCommandById('ghost-sync:browse-posts');
      });

      // Wait for API calls
      await new Promise(resolve => setTimeout(resolve, 3000));
    } else {
      console.log("⚠️  Ghost settings not configured, skipping API call test");
    }

    // In recording mode, we should see real API calls
    // In replay mode, we should see mocked responses (fewer or no actual network requests)
    console.log(`Total Ghost API requests intercepted: ${networkRequests.length}`);
    console.log(`Total requests intercepted: ${allRequests.length}`);
    console.log("Ghost API requests:", networkRequests);

    // This test mainly serves to demonstrate the mocking is working
    // The exact behavior depends on whether we're recording or replaying
    expect(true).toBe(true); // Always pass, this is just for demonstration
  });
});
